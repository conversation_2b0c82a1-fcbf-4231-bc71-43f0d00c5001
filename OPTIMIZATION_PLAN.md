# Rencana Optimisasi: Analysis Worker → Archive Service

## 📋 Overview
Dokumen ini menjelaskan rencana optimisasi untuk meningkatkan performa penyimpanan data dari analysis-worker ke archive-service, fokus pada batching, connection pooling, dan database optimization.

## 🎯 Tujuan Utama
1. **Meningkatkan throughput** penyimpanan data hingga 3x lipat
2. **Mengurangi latency** rata-rata dari 2 detik menjadi <500ms
3. **Meningkatkan reliability** dengan better error handling
4. **Mengurangi database load** melalui optimized batching

## 📊 Masalah Saat Ini
- Batch size tidak selaras (worker: 10, archive: 50)
- Timeout berbeda menyebabkan inefficiency
- HTTP connection tidak optimal
- Database pool tidak maksimal

---

## 🔧 Phase 1: Batching Configuration Alignment

### File yang Diubah:
1. `analysis-worker/.env.example`
2. `archive-service/.env.example`
3. `analysis-worker/src/services/optimizedArchiveService.js`

### Perubahan:

#### 1.1 Update Environment Variables
**File: `analysis-worker/.env.example`**
```bash
# Database Optimization Configuration (UPDATED)
DB_BATCH_SIZE=50                    # Naik dari 10 → 50
DB_BATCH_INTERVAL=1500             # Turun dari 5000 → 1500ms
DB_BATCH_MAX_QUEUE=2000            # Baru: max queue size

# HTTP Connection Optimization (BARU)
HTTP_KEEP_ALIVE_TIMEOUT=30000      # 30 detik
HTTP_MAX_SOCKETS_PER_HOST=20       # Per host limit
HTTP_TOTAL_MAX_SOCKETS=100         # Total limit
```

**File: `archive-service/.env.example`**
```bash
# Batch Processing Configuration (UPDATED)
BATCH_MAX_SIZE=100                 # Naik dari 50 → 100
BATCH_TIMEOUT=2000                 # Tetap 2 detik
BATCH_MAX_QUEUE_SIZE=2000          # Naik dari 1000 → 2000

# Database Pool Optimization (UPDATED)
DB_POOL_MAX=100                    # Naik dari 75 → 100
DB_POOL_MIN=20                     # Naik dari 15 → 20
DB_POOL_ACQUIRE=20000              # Turun dari 25000 → 20000
```

### Tujuan:
- **Selaraskan batch processing** antara worker dan archive
- **Tingkatkan throughput** dengan batch size lebih besar
- **Kurangi latency** dengan interval lebih cepat

### Efek ke File Lain:
- ✅ **Tidak ada breaking changes**
- ✅ **Backward compatible** (semua default values tetap ada)
- ⚠️ **Memory usage** akan sedikit naik (monitoring diperlukan)

### Yang Harus Dihindari:
- ❌ **Jangan set batch size > 200** (risk of memory overflow)
- ❌ **Jangan set interval < 1000ms** (terlalu aggressive)
- ❌ **Jangan ubah tanpa monitoring** resource usage

---

## 🔧 Phase 2: Connection Pool Enhancement

### File yang Diubah:
1. `analysis-worker/src/services/optimizedArchiveService.js`
2. `archive-service/src/config/database.js`

### Perubahan:

#### 2.1 Enhanced HTTP Agent
**File: `analysis-worker/src/services/optimizedArchiveService.js`**
```javascript
// TAMBAH: Enhanced HTTP agents dengan monitoring
const http = require('http');
const https = require('https');

const httpAgent = new http.Agent({
  keepAlive: true,
  keepAliveMsecs: parseInt(process.env.HTTP_KEEP_ALIVE_TIMEOUT || '30000'),
  maxSockets: parseInt(process.env.HTTP_MAX_SOCKETS_PER_HOST || '20'),
  maxTotalSockets: parseInt(process.env.HTTP_TOTAL_MAX_SOCKETS || '100'),
  timeout: 60000,
  // BARU: Connection monitoring
  scheduling: 'fifo'
});

// TAMBAH: Connection health monitoring
httpAgent.on('free', (socket, options) => {
  logger.debug('HTTP connection freed', { 
    host: options.host, 
    port: options.port 
  });
});
```

#### 2.2 Database Pool Monitoring
**File: `archive-service/src/config/database.js`**
```javascript
// TAMBAH: Pool event monitoring
sequelize.connectionManager.pool.on('acquire', (connection) => {
  logger.debug('Database connection acquired', { 
    totalCount: sequelize.connectionManager.pool.size,
    idleCount: sequelize.connectionManager.pool.available 
  });
});

sequelize.connectionManager.pool.on('release', (connection) => {
  logger.debug('Database connection released');
});
```

### Tujuan:
- **Monitor connection health** secara real-time
- **Optimize connection reuse** untuk mengurangi overhead
- **Detect connection leaks** lebih cepat

### Efek ke File Lain:
- ✅ **Logging output** akan bertambah (perlu log rotation)
- ✅ **Monitoring metrics** tersedia untuk observability
- ⚠️ **Slight performance overhead** dari monitoring

### Yang Harus Dihindari:
- ❌ **Jangan enable debug logging** di production
- ❌ **Jangan set maxSockets terlalu tinggi** (>50 per host)
- ❌ **Jangan lupa implement log rotation**

---

## 🔧 Phase 3: Database Optimization

### File yang Diubah:
1. `archive-service/src/services/resultsService.js`
2. **BARU:** `archive-service/migrations/add-performance-indexes.js`

### Perubahan:

#### 3.1 Enhanced Bulk Operations
**File: `archive-service/src/services/resultsService.js`**
```javascript
// TAMBAH: Prepared statement untuk bulk insert
async bulkCreateResults(dataArray) {
  const transaction = await AnalysisResult.sequelize.transaction({
    isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED
  });

  try {
    // BARU: Chunk large batches untuk memory efficiency
    const chunkSize = 50;
    const chunks = [];
    for (let i = 0; i < dataArray.length; i += chunkSize) {
      chunks.push(dataArray.slice(i, i + chunkSize));
    }

    const allResults = [];
    for (const chunk of chunks) {
      const results = await AnalysisResult.bulkCreate(chunk, {
        transaction,
        returning: true,
        validate: true,
        ignoreDuplicates: false,
        // BARU: Optimize untuk PostgreSQL
        logging: false
      });
      allResults.push(...results);
    }

    await transaction.commit();
    return allResults;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

#### 3.2 Database Indexes
**File: `archive-service/migrations/add-performance-indexes.js`** (BARU)
```javascript
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Index untuk query berdasarkan user_id dan created_at
    await queryInterface.addIndex('analysis_results', 
      ['user_id', 'created_at'], 
      {
        name: 'idx_analysis_results_user_created',
        concurrently: true
      }
    );

    // Index untuk status queries
    await queryInterface.addIndex('analysis_results', 
      ['status', 'created_at'], 
      {
        name: 'idx_analysis_results_status_created',
        concurrently: true
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('analysis_results', 'idx_analysis_results_user_created');
    await queryInterface.removeIndex('analysis_results', 'idx_analysis_results_status_created');
  }
};
```

### Tujuan:
- **Chunking** untuk handle large batches tanpa memory issues
- **Database indexes** untuk faster queries
- **Transaction optimization** untuk better concurrency

### Efek ke File Lain:
- ✅ **Query performance** meningkat untuk semua endpoints
- ✅ **Memory usage** lebih stabil dengan chunking
- ⚠️ **Migration** perlu dijalankan di semua environments

### Yang Harus Dihindari:
- ❌ **Jangan buat index tanpa CONCURRENTLY** (locks table)
- ❌ **Jangan set chunk size < 10** (overhead tinggi)
- ❌ **Jangan lupa backup database** sebelum migration

---

## 🔧 Phase 4: Monitoring & Metrics

### File yang Diubah:
1. **BARU:** `analysis-worker/src/utils/performanceMonitor.js`
2. **BARU:** `archive-service/src/middleware/batchMetrics.js`

### Perubahan:

#### 4.1 Performance Monitor
**File: `analysis-worker/src/utils/performanceMonitor.js`** (BARU)
```javascript
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      batchProcessingTime: [],
      httpConnectionReuse: 0,
      queueDepth: 0,
      errorRate: 0
    };
  }

  recordBatchProcessing(startTime, batchSize) {
    const duration = Date.now() - startTime;
    this.metrics.batchProcessingTime.push({
      duration,
      batchSize,
      timestamp: Date.now()
    });

    // Keep only last 100 records
    if (this.metrics.batchProcessingTime.length > 100) {
      this.metrics.batchProcessingTime.shift();
    }
  }

  getAverageProcessingTime() {
    const times = this.metrics.batchProcessingTime;
    if (times.length === 0) return 0;
    
    const total = times.reduce((sum, record) => sum + record.duration, 0);
    return total / times.length;
  }
}
```

### Tujuan:
- **Real-time monitoring** performa batching
- **Metrics collection** untuk optimization decisions
- **Alert system** untuk performance degradation

### Efek ke File Lain:
- ✅ **Observability** meningkat drastis
- ✅ **Debugging** jadi lebih mudah
- ⚠️ **Memory footprint** sedikit bertambah

### Yang Harus Dihindari:
- ❌ **Jangan store metrics tanpa limit** (memory leak)
- ❌ **Jangan log metrics terlalu sering** (log spam)
- ❌ **Jangan expose sensitive data** dalam metrics

---

## 📈 Expected Results

### Performance Improvements:
- **Throughput**: 100 → 300 records/second
- **Latency**: 2000ms → 500ms average
- **Database Load**: 40% reduction
- **Memory Usage**: 15% increase (acceptable)

### Monitoring Capabilities:
- Real-time batch processing metrics
- Connection pool utilization
- Database performance tracking
- Error rate monitoring

---

## ⚠️ Risks & Mitigation

### High Risk:
1. **Memory Usage Spike**
   - **Mitigation**: Gradual rollout, monitoring
   - **Rollback**: Revert batch sizes to original values

2. **Database Lock Issues**
   - **Mitigation**: Use CONCURRENTLY for index creation
   - **Rollback**: Drop indexes if performance degrades

### Medium Risk:
1. **Connection Pool Exhaustion**
   - **Mitigation**: Proper pool sizing, monitoring
   - **Rollback**: Reduce pool sizes

---

## 🚀 Implementation Timeline

### Week 1: Phase 1 (Batching Alignment)
- Update environment variables
- Test in development
- Monitor resource usage

### Week 2: Phase 2 (Connection Enhancement)
- Implement enhanced HTTP agents
- Add connection monitoring
- Performance testing

### Week 3: Phase 3 (Database Optimization)
- Create and run migrations
- Implement chunking
- Load testing

### Week 4: Phase 4 (Monitoring)
- Implement performance monitoring
- Set up alerts
- Documentation

---

## ✅ Success Criteria

1. **Throughput** meningkat minimal 2x
2. **Latency** turun minimal 50%
3. **Zero downtime** during implementation
4. **No data loss** during optimization
5. **Monitoring** berfungsi dengan baik

---

## 📞 Support & Rollback

### Rollback Plan:
1. Revert environment variables
2. Drop new indexes (if needed)
3. Disable new monitoring
4. Restart services

### Support Contacts:
- **Database Issues**: DBA Team
- **Performance Issues**: DevOps Team
- **Application Issues**: Development Team
